{"accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138, "ran_extra_deduplication": true}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1068, "left": 184, "maximized": true, "right": 1129, "top": 48, "work_area_bottom": 1040, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "68d0996d-f51a-4b8b-bd2c-bdda59be0a44", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "138.0.7204.92"}, "gaia_cookie": {"changed_time": **********.678244, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_binary_data": ""}, "gcm": {"product_category_for_subtypes": "com.googlechromefortesting.windows"}, "google": {"services": {"signin_scoped_device_id": "5a04af5d-0e46-4bc7-8e93-f0a62e61c1a7"}}, "https_upgrade_navigations": {"2025-07-21": 630}, "in_product_help": {"recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-US,en"}, "media": {"engagement": {"schema_version": 5}}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 8}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "plus_addresses": {"preallocation": {"addresses": [], "next": 0}}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true, "first_party_sets_enabled": false, "fledge_join_blocked": {}}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]darbydental.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]darbytechforce.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://darbytechforce.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.darbydental.com:443,*": {"expiration": "13405334628169121", "last_modified": "13397558628169124", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 7}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://darbytechforce.com:443,*": {"last_modified": "13397558176010460", "setting": {"lastEngagementTime": 1.3397558176010446e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "https://www.darbydental.com:443,*": {"last_modified": "13397565255968779", "setting": {"lastEngagementTime": 1.3397565255968764e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 22.2}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.7204.92", "creation_time": "13397554326318175", "exit_type": "Crashed", "family_member_role": "not_in_family", "last_engagement_time": "13397565255968763", "last_time_obsolete_http_credentials_removed": 1753083465.836229, "last_time_password_store_metrics_reported": 1753081993.573598, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chromium", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13397554326", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": true, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQsoTYlpOg5hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEN2E2JaToOYX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13397443199000000", "uma_in_sql_start_time": "13397554326343722"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13397555544972388", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397555563559199", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13397555608686964", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397556392798878", "type": 0}, {"crashed": true, "time": "13397556957970157", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 2, "time": "13397556963850371", "type": 2, "window_count": 1}, {"crashed": true, "time": "13397557005834896", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13397557127451307", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397557625922969", "type": 0}, {"crashed": true, "time": "13397557712770352", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 2, "time": "13397558180616132", "type": 2, "window_count": 1}, {"crashed": true, "time": "13397558229983967", "type": 0}, {"crashed": true, "time": "13397558370021861", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13397558628148618", "type": 2, "window_count": 1}, {"crashed": false, "time": "13***************", "type": 0}, {"crashed": true, "time": "*****************", "type": 0}, {"crashed": true, "time": "*****************", "type": 0}, {"crashed": true, "time": "*****************", "type": 0}, {"crashed": true, "time": "*****************", "type": 0}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true, "tab_search_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138"}}